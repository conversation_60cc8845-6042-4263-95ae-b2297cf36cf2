<template>
  <div class="service-list">
    <!-- 顶部导航 -->
    <TopNav title="服务管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="项目名称" prop="title">
                <el-input
                  size="default"
                  v-model="searchForm.title"
                  placeholder="请输入项目名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="可用" :value="1" />
                  <el-option label="禁用" :value="-1" />
                </el-select>
              </el-form-item>
              <el-form-item label="分类名称" prop="serviceCateName">
                <el-input
                  size="default"
                  v-model="searchForm.serviceCateName"
                  placeholder="请输入分类名称"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>
              <el-form-item label="报价类型" prop="servicePriceType">
                <el-select
                  size="default"
                  v-model="searchForm.servicePriceType"
                  placeholder="请选择报价类型"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="一口价模式" :value="0" />
                  <el-option label="报价模式" :value="1" />
                  <el-option label="两者都有" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增服务
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>



      <!-- 表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
        >
        <!-- <el-table-column type="selection" width="55" /> -->

        <el-table-column prop="id" label="ID" width="80" align="center" />

        <el-table-column prop="cover" label="封面图" width="120">
          <template #default="scope">
            <LbImage :src="scope.row.cover" width="80" height="50" />
          </template>
        </el-table-column>

        <el-table-column prop="title" label="项目名称" min-width="150">
          <template #default="scope">
            <div>
              <p style="margin: 0; font-weight: 500;">{{ scope.row.title }}</p>
              <p style="margin: 0; color: #999; font-size: 18px;" v-if="scope.row.subTitle">
                {{ scope.row.subTitle }}
              </p>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="serviceCateName" label="服务类型" width="120" />

        <el-table-column prop="price" label="价格" width="100" align="center">
          <template #default="scope">
            <span style="color: #f56c6c; font-weight: 500;">
              ¥{{ scope.row.price }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="servicePriceType" label="报价类型" width="120" align="center">
          <template #default="scope">
            <el-tag
              :type="scope.row.servicePriceType === 0 ? 'success' : scope.row.servicePriceType === 1 ? 'warning' : 'info'"
            >
              {{ getPriceTypeText(scope.row.servicePriceType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="totalSale" label="销量" width="80" align="center" />

        <el-table-column prop="top" label="排序值" width="80" align="center" />

        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="-1"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="时间" width="160">
          <template #default="scope">
            <div class="time-column">
              <p>{{ formatDate(scope.row.createTime) }}</p>
              <p>{{ formatTime(scope.row.createTime) }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <LbButton
              size="default"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </LbButton>
            <LbButton
              size="default"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </LbButton>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>


  </div>
</template>



<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbImage from '@/components/common/LbImage.vue'
import LbPage from '@/components/common/LbPage.vue'

// 导入API
import { api } from '@/api-v2'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  title: '',
  status: null,
  serviceCateName: '',
  servicePriceType: null
})

// 引用
const searchFormRef = ref()



// 获取列表数据
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.title) params.title = searchForm.title
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status
    if (searchForm.serviceCateName) params.serviceCateName = searchForm.serviceCateName
    if (searchForm.servicePriceType !== null && searchForm.servicePriceType !== '') {
      params.servicePriceType = searchForm.servicePriceType
    }

    // 使用API-V2调用方式
    const result = await api.service.serviceList(params)
    console.log('📋 服务列表数据 (API-V2):', result)

    // 处理API响应格式
    if (result.code === '200') {
      const data = result.data
      tableData.value = data.list || []
      total.value = data.totalCount || data.total || 0

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.message || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取服务列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取报价类型文本
const getPriceTypeText = (type) => {
  const typeMap = {
    0: '一口价',
    1: '报价模式',
    2: '两者都有'
  }
  return typeMap[type] || '未知'
}

// 搜索处理
const handleSearch = () => {
  getTableDataList(1)
}

// 重置处理
const handleReset = () => {
  searchForm.title = ''
  searchForm.status = null
  searchForm.serviceCateName = ''
  searchForm.servicePriceType = null
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

// 新增处理
const handleAdd = () => {
  // 跳转到新增页面
  router.push({
    name: 'ServiceEdit',
    query: { type: 'add' }
  })
}

// 编辑处理
const handleEdit = (row) => {
  // 跳转到编辑页面，传递服务ID
  router.push({
    name: 'ServiceEdit',
    query: {
      type: 'edit',
      id: row.id
    }
  })
}

// 删除处理
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个服务吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await api.service.serviceDelete({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除服务失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 状态切换处理
const handleStatusChange = async (row) => {
  try {
    const result = await api.service.serviceStatus({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('状态修改成功')
    } else {
      // 恢复原状态
      row.status = row.status === 1 ? -1 : 1
      ElMessage.error(result.message || '状态修改失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? -1 : 1
    console.error('修改状态失败:', error)
    ElMessage.error('状态修改失败')
  }
}



// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}



// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dateString.split(' ')[0]
}

const formatTime = (dateString) => {
  if (!dateString) return '-'
  return dateString.split(' ')[1]
}

// 生命周期
onMounted(() => {
  getTableDataList()
})
</script>

<style scoped>
.service-list {
  padding: 0px;
}
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
} 
.action-buttons {
  margin-bottom: 20px;
}

.time-column p {
  margin: 0;
  line-height: 1.8;
  font-size: 18px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}





/* 表格样式优化 */
:deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-table .el-table__row:hover > td) {
  background-color: #f5f7fa;
}

/* 价格样式 */
.price-text {
  color: #f56c6c;
  font-weight: 500;
  font-size: 18px;
}

/* 状态标签样式 */
.status-tag {
  font-size: 18px;
}

/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* Element Plus 输入框样式覆盖 - 与 ServiceEdit.vue 保持一致 */

/* 强制重置所有可能的 input 元素 */
.search-form :deep(input),
.search-form :deep(input[type="text"]),
.search-form :deep(input[type="password"]),
.search-form :deep(input[type="email"]),
.search-form :deep(input[type="number"]),
.search-form :deep(input[type="tel"]),
.search-form :deep(input[type="url"]),
.search-form :deep(input[type="search"]),
.search-form :deep(input[type="date"]),
.search-form :deep(input[type="time"]),
.search-form :deep(input[type="datetime-local"]) {
  border: none !important;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
  border-style: none !important;
  border-width: 0 !important;
  border-color: transparent !important;
  border-radius: 0 !important;
}

/* 外层容器样式 */
.search-form :deep(.el-input__wrapper),
.search-form :deep(.el-textarea__inner) {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
  box-shadow: none !important;
  background-color: #ffffff;
}

/* 输入框内部的原生 input 元素 */
.search-form :deep(.el-input__inner) {
  border: none !important;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  height: auto !important;
  border-style: none !important;
  border-width: 0 !important;
  border-color: transparent !important;
  font-size: 14px;
}

/* 焦点状态 */
.search-form :deep(.el-input__wrapper.is-focus),
.search-form :deep(.el-textarea__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 确保焦点状态下内部 input 仍然无边框 */
.search-form :deep(.el-input__wrapper.is-focus .el-input__inner),
.search-form :deep(.el-input__wrapper:focus-within .el-input__inner) {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 悬停状态 */
.search-form :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

/* 禁用状态 */
.search-form :deep(.el-input__wrapper.is-disabled) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

.search-form :deep(.el-input__wrapper.is-disabled .el-input__inner) {
  background-color: transparent !important;
  cursor: not-allowed;
  color: #c0c4cc;
}

/* 选择器样式 */
.search-form :deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  box-shadow: none !important;
}

.search-form :deep(.el-select .el-input__inner) {
  border: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }
}
</style>