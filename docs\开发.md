# Service 模块开发指南

## 目录

- [模块概述](#模块概述)
- [页面功能详解](#页面功能详解)
  - [服务管理](#1-服务管理)
  - [轮播图管理](#2-轮播图管理)
  - [金刚区管理](#3-金刚区管理)
  - [服务分类管理](#4-服务分类管理)
- [API 接口文档](#api-接口文档)
- [组件结构](#组件结构)
- [开发规范](#开发规范)
- [快速开发指南](#快速开发指南)
- [常见问题](#常见问题)

## 模块概述

Service 模块是后台管理系统中负责服务相关功能的模块，包括服务管理、轮播图管理、金刚区管理和服务分类管理等功能。该模块主要用于管理用户端和师傅端的服务展示和配置。

## 页面功能详解

### 1. 服务管理

**文件路径**: 
- 列表页：`src/views/service/ServiceList.vue`
- 编辑页：`src/views/service/ServiceEdit.vue`

**功能描述**:
- 服务列表展示与分页
- 服务搜索（按名称、状态等）
- 服务新增/编辑/删除
- 服务状态管理（启用/禁用）
- 服务详情查看

**数据结构**:
```javascript
// 服务项数据结构
{
  id: Number,           // 服务ID
  title: String,        // 服务标题
  img: String,          // 服务图片
  price: Number,        // 服务价格
  category_id: Number,  // 分类ID
  status: Number,       // 状态：1-可用，-1-不可用
  create_time: String,  // 创建时间
  update_time: String,  // 更新时间
  description: String,  // 服务描述
  // 其他字段...
}
```

**页面截图**:
*(此处可放置页面截图)*

### 2. 轮播图管理

**文件路径**: `src/views/service/ServiceBanner.vue`

**功能描述**:
- 轮播图列表展示与分页
- 轮播图搜索（按标题、状态等）
- 轮播图新增/编辑/删除
- 轮播图状态管理（启用/禁用）
- 轮播图排序管理

**数据结构**:
```javascript
// 轮播图数据结构
{
  id: Number,           // 轮播图ID
  title: String,        // 轮播图标题
  img: String,          // 轮播图图片URL
  link: String,         // 跳转链接
  top: Number,          // 排序值（数值越大越靠前）
  status: Number,       // 状态：1-可用，-1-不可用
  create_time: String,  // 创建时间
  update_time: String,  // 更新时间
  type: Number          // 类型：1-用户端，2-师傅端
}
```

**页面截图**:
*(此处可放置页面截图)*

### 3. 金刚区管理

**文件路径**: `src/views/service/ServiceNav.vue`

**功能描述**:
- 金刚区列表展示与分页
- 金刚区搜索（按标题、状态、类型等）
- 金刚区新增/编辑/删除
- 金刚区状态管理（启用/禁用）
- 金刚区排序管理

**数据结构**:
```javascript
// 金刚区数据结构
{
  id: Number,           // 金刚区ID
  title: String,        // 金刚区标题
  img: String,          // 金刚区图标URL
  link: String,         // 跳转链接
  top: Number,          // 排序值（数值越大越靠前）
  status: Number,       // 状态：1-可用，-1-不可用
  create_time: String,  // 创建时间
  update_time: String,  // 更新时间
  type: Number          // 类型：1-用户端，2-师傅端
}
```

**页面截图**:
*(此处可放置页面截图)*

### 4. 服务分类管理

**文件路径**: `src/views/service/ServiceFenlei.vue`

**功能描述**:
- 分类列表展示与分页
- 分类搜索（按名称、状态等）
- 分类新增/编辑/删除
- 分类状态管理（启用/禁用）
- 分类排序管理

**数据结构**:
```javascript
// 服务分类数据结构
{
  id: Number,           // 分类ID
  name: String,         // 分类名称
  img: String,          // 分类图标URL
  top: Number,          // 排序值（数值越大越靠前）
  status: Number,       // 状态：1-可用，-1-不可用
  create_time: String,  // 创建时间
  update_time: String   // 更新时间
}
```

**页面截图**:
*(此处可放置页面截图)*

## API 接口文档

所有 API 接口都通过统一的 `api.service` 命名空间进行调用。

### 服务管理接口

| 接口名称 | 方法 | 描述 | 参数 |
|---------|------|------|------|
| `api.service.list` | GET | 获取服务列表 | `{ pageNum, pageSize, title, status, category_id }` |
| `api.service.add` | POST | 新增服务 | `{ title, img, price, category_id, description, status, ... }` |
| `api.service.update` | POST | 更新服务 | `{ id, title, img, price, category_id, description, status, ... }` |
| `api.service.delete` | POST | 删除服务 | `{ id }` |
| `api.service.status` | POST | 更新服务状态 | `{ id, status }` |
| `api.service.detail` | GET | 获取服务详情 | `{ id }` |

### 轮播图管理接口

| 接口名称 | 方法 | 描述 | 参数 |
|---------|------|------|------|
| `api.service.bannerList` | GET | 获取轮播图列表 | `{ pageNum, pageSize, title, status, type }` |
| `api.service.bannerAdd` | POST | 新增轮播图 | `{ title, img, link, top, status, type }` |
| `api.service.bannerUpdate` | POST | 更新轮播图 | `{ id, title, img, link, top, status, type }` |
| `api.service.bannerDelete` | POST | 删除轮播图 | `{ id }` |
| `api.service.bannerStatus` | POST | 更新轮播图状态 | `{ id, status }` |

### 金刚区管理接口

| 接口名称 | 方法 | 描述 | 参数 |
|---------|------|------|------|
| `api.service.navList` | GET | 获取金刚区列表 | `{ pageNum, pageSize, title, status, type }` |
| `api.service.navAdd` | POST | 新增金刚区 | `{ title, img, link, top, status, type }` |
| `api.service.navUpdate` | POST | 更新金刚区 | `{ id, title, img, link, top, status, type }` |
| `api.service.navDelete` | POST | 删除金刚区 | `{ id }` |

### 服务分类管理接口

| 接口名称 | 方法 | 描述 | 参数 |
|---------|------|------|------|
| `api.service.categoryList` | GET | 获取分类列表 | `{ pageNum, pageSize, name, status }` |
| `api.service.categoryAdd` | POST | 新增分类 | `{ name, img, top, status }` |
| `api.service.categoryUpdate` | POST | 更新分类 | `{ id, name, img, top, status }` |
| `api.service.categoryDelete` | POST | 删除分类 | `{ id }` |
| `api.service.categoryStatus` | POST | 更新分类状态 | `{ id, status }` |

## 组件结构

Service 模块使用了以下公共组件：

### 1. TopNav 组件

**路径**: `@/components/common/TopNav.vue`

**用途**: 页面顶部导航栏，显示页面标题和返回按钮

**使用示例**:
```html
<TopNav title="服务管理" />
```

### 2. LbButton 组件

**路径**: `@/components/common/LbButton.vue`

**用途**: 统一样式的按钮组件，支持图标

**使用示例**:
```html
<LbButton type="primary" icon="Plus" @click="handleAdd">新增</LbButton>
<LbButton icon="RefreshLeft" @click="handleReset">重置</LbButton>
```

### 3. LbImage 组件

**路径**: `@/components/common/LbImage.vue`

**用途**: 图片展示组件，支持预览

**使用示例**:
```html
<LbImage :src="scope.row.img" width="80" height="50" />
```

### 4. LbPage 组件

**路径**: `@/components/common/LbPage.vue`

**用途**: 分页组件

**使用示例**:
```html
<LbPage
  :page="searchForm.pageNum"
  :page-size="searchForm.pageSize"
  :total="total"
  @handleSizeChange="handleSizeChange"
  @handleCurrentChange="handleCurrentChange"
/>
```

## 开发规范

### 1. 页面结构规范

所有 Service 模块的页面应遵循以下结构：

```html
<template>
  <div class="service-xxx">
    <!-- 顶部导航 -->
    <TopNav title="页面标题" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form :model="searchForm" :inline="true" class="search-form">
          <!-- 搜索字段 -->
          <el-form-item>
            <LbButton type="primary" icon="Search" @click="handleSearch">搜索</LbButton>
            <LbButton icon="RefreshLeft" @click="handleReset">重置</LbButton>
            <LbButton type="primary" icon="Plus" @click="handleAdd">新增</LbButton>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <el-table v-loading="loading" :data="tableData">
          <!-- 表格列 -->
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle">
      <el-form :model="form" :rules="rules" label-width="100px">
        <!-- 表单字段 -->
      </el-form>
      <template #footer>
        <LbButton @click="dialogVisible = false">取消</LbButton>
        <LbButton type="primary" @click="handleSubmit">确定</LbButton>
      </template>
    </el-dialog>
  </div>
</template>
```

### 2. 样式规范

所有 Service 模块的页面应使用以下统一样式：

```css
/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格头部样式 */
.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

/* 表格单元格样式 */
.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}
```

### 3. 数据处理规范

#### 列表数据获取

```javascript
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize,
      // 添加其他搜索条件
      ...searchForm
    }
    
    // 调用API获取数据
    const res = await api.service.list(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      total.value = res.data.total
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

#### 表单提交处理

```javascript
const handleSubmit = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const apiMethod = isEdit.value ? api.service.update : api.service.add
        const res = await apiMethod(form.value)
        if (res.code === 200) {
          ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
          dialogVisible.value = false
          getTableDataList(true)
        } else {
          ElMessage.error(res.msg || (isEdit.value ? '编辑失败' : '新增失败'))
        }
      } catch (error) {
        console.error(isEdit.value ? '编辑失败' : '新增失败', error)
        ElMessage.error(isEdit.value ? '编辑失败' : '新增失败')
      } finally {
        loading.value = false
      }
    }
  })
}
```

#### 状态更新处理

```javascript
const handleStatusChange = async (row) => {
  const newStatus = row.status === 1 ? -1 : 1
  try {
    const res = await api.service.status({
      id: row.id,
      status: newStatus
    })
    if (res.code === 200) {
      ElMessage.success('状态更新成功')
      row.status = newStatus
    } else {
      ElMessage.error(res.msg || '状态更新失败')
    }
  } catch (error) {
    console.error('状态更新失败', error)
    ElMessage.error('状态更新失败')
  }
}
```

### 4. 事件处理规范

#### 搜索和重置

```javascript
const handleSearch = () => {
  getTableDataList(true)
}

const handleReset = () => {
  // 重置搜索表单
  searchForm.value = {
    pageNum: 1,
    pageSize: 10,
    // 重置其他搜索条件为默认值
  }
  getTableDataList(true)
}
```

#### 分页处理

```javascript
const handleSizeChange = (val) => {
  searchForm.pageSize = val
  getTableDataList(true)
}

const handleCurrentChange = (val) => {
  searchForm.pageNum = val
  getTableDataList(false)
}
```

#### 新增和编辑

```javascript
const handleAdd = () => {
  isEdit.value = false
  dialogTitle.value = '新增'
  form.value = {
    // 设置默认值
    status: 1
  }
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  dialogTitle.value = '编辑'
  form.value = JSON.parse(JSON.stringify(row)) // 深拷贝，避免直接修改表格数据
  dialogVisible.value = true
}
```

## 快速开发指南

### 1. 创建新的服务管理页面

1. **复制模板文件**

   复制现有的 Service 模块页面作为模板，例如 `ServiceList.vue`，并重命名为你的新页面名称。

2. **修改页面标题和组件名称**

   ```html
   <template>
     <div class="service-new-page">
       <TopNav title="新页面标题" />
       <!-- 其他内容 -->
     </div>
   </template>

   <script>
   export default {
     name: 'ServiceNewPage'
     // ...
   }
   </script>
   ```

3. **调整数据结构和API调用**

   根据你的需求修改数据结构和API调用：

   ```javascript
   // 修改搜索表单
   const searchForm = ref({
     pageNum: 1,
     pageSize: 10,
     // 添加你需要的搜索字段
   })

   // 修改表单数据结构
   const form = ref({
     // 定义你的表单字段
   })

   // 修改API调用
   const getTableDataList = async (flag) => {
     // ...
     const res = await api.service.yourNewApi(params)
     // ...
   }
   ```

4. **调整表格列**

   根据你的数据结构修改表格列：

   ```html
   <el-table v-loading="loading" :data="tableData">
     <el-table-column prop="id" label="ID" width="80" />
     <!-- 添加你需要的列 -->
     <el-table-column label="操作" width="200" fixed="right">
       <template #default="scope">
         <!-- 添加你需要的操作按钮 -->
       </template>
     </el-table-column>
   </el-table>
   ```

5. **调整表单字段**

   根据你的数据结构修改表单字段：

   ```html
   <el-form :model="form" :rules="rules" label-width="100px">
     <!-- 添加你需要的表单字段 -->
     <el-form-item label="名称" prop="name">
       <el-input v-model="form.name" placeholder="请输入名称" />
     </el-form-item>
     <!-- 其他表单字段 -->
   </el-form>
   ```

### 2. 添加新的API接口

1. **在 api-v2/modules/service.js 中添加新接口**

   ```javascript
   // 添加新的API接口
   export const yourNewApi = (params) => {
     return request({
       url: '/api/service/your-new-endpoint',
       method: 'get',
       params
     })
   }

   // 或者POST请求
   export const yourNewPostApi = (data) => {
     return request({
       url: '/api/service/your-new-endpoint',
       method: 'post',
       data
     })
   }
   ```

2. **在 api-v2/index.js 中注册新接口**

   确保你的新接口已经在 API 索引中注册：

   ```javascript
   import * as service from './modules/service'

   export default {
     service
     // 其他模块...
   }
   ```

## 功能模块实现指南

### 1. 统计卡片模块

统计卡片模块用于在页面顶部展示重要的统计数据，提供直观的数据概览。

#### 实现示例

```html
<!-- 统计卡片 -->
<el-row :gutter="20" class="stats-cards">
  <el-col :span="6">
    <el-card class="stat-card">
      <div class="stat-content">
        <div class="stat-value">{{ totalServices || 0 }}</div>
        <div class="stat-label">服务总数</div>
      </div>
    </el-card>
  </el-col>
  <el-col :span="6">
    <el-card class="stat-card">
      <div class="stat-content">
        <div class="stat-value">¥{{ totalAmount || '0.00' }}</div>
        <div class="stat-label">服务总金额</div>
      </div>
    </el-card>
  </el-col>
  <el-col :span="6">
    <el-card class="stat-card">
      <div class="stat-content">
        <div class="stat-value">{{ activeServices || 0 }}</div>
        <div class="stat-label">活跃服务</div>
      </div>
    </el-card>
  </el-col>
  <el-col :span="6">
    <el-card class="stat-card">
      <div class="stat-content">
        <div class="stat-value">{{ categoryCount || 0 }}</div>
        <div class="stat-label">服务分类</div>
      </div>
    </el-card>
  </el-col>
</el-row>
```

#### 样式实现

```css
/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 20px;
}

/* 统计内容布局 */
.stat-content {
  text-align: center;
  padding: 10px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}
```

#### 数据获取

```javascript
// 统计数据
const totalServices = ref(0)
const totalAmount = ref('0.00')
const activeServices = ref(0)
const categoryCount = ref(0)

// 获取统计数据
const getStatistics = async () => {
  try {
    const res = await api.service.statistics()
    if (res.code === 200) {
      totalServices.value = res.data.totalServices || 0
      totalAmount.value = res.data.totalAmount || '0.00'
      activeServices.value = res.data.activeServices || 0
      categoryCount.value = res.data.categoryCount || 0
    }
  } catch (error) {
    console.error('获取统计数据失败', error)
  }
}

// 在页面加载时调用
onMounted(() => {
  getStatistics()
  getTableDataList(true)
})
```

### 2. 文件下载功能

文件下载功能用于导出数据或下载文件，支持两种方式：GET 请求下载和 POST 请求下载。

#### GET 请求下载

适用于简单的下载场景，参数通过 URL 传递。

```javascript
/**
 * GET 方式下载文件
 * @param {Object} params - 查询参数
 */
const downloadFileByGet = async (params = {}) => {
  try {
    // 显示加载状态
    exportLoading.value = true
    
    // 构建查询参数
    const queryParams = new URLSearchParams()
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        queryParams.append(key, params[key])
      }
    })
    
    // 获取 token
    const token = sessionStorage.getItem('minitk')
    
    // 构建下载 URL
    const downloadUrl = `/api/admin/service/export?${queryParams.toString()}`
    
    // 发起请求
    const response = await fetch(downloadUrl, {
      method: 'GET',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` })
      }
    })
    
    // 处理响应
    if (response.ok) {
      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = `服务数据导出_${new Date().toISOString().slice(0, 10)}.xlsx`
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
          // 处理中文文件名
          try {
            filename = decodeURIComponent(filename)
          } catch (e) {
            // 如果解码失败，使用原始文件名
          }
        }
      }
      
      // 创建 blob 并下载
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // 清理 URL 对象
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('导出成功')
    } else {
      // 处理错误
      const errorText = await response.text()
      console.error('下载失败:', response.status, errorText)
      ElMessage.error(`下载失败: ${response.statusText}`)
    }
  } catch (error) {
    console.error('下载文件异常:', error)
    ElMessage.error('下载失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}
```

#### POST 请求下载

适用于复杂的下载场景，参数通过请求体传递。

```javascript
/**
 * POST 方式下载文件
 * @param {Object} data - 请求体数据
 */
const downloadFileByPost = async (data = {}) => {
  try {
    // 显示加载状态
    exportLoading.value = true
    
    // 获取 token
    const token = sessionStorage.getItem('minitk')
    
    // 发起请求
    const response = await fetch('/api/admin/service/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: JSON.stringify(data)
    })
    
    // 处理响应
    if (response.ok) {
      // 检查响应内容类型
      const contentType = response.headers.get('Content-Type')
      
      // 如果是 JSON 响应，说明可能是错误信息
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json()
        console.error('导出返回错误:', errorData)
        ElMessage.error(errorData.msg || '导出失败')
        return
      }
      
      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = `服务数据导出_${new Date().toISOString().slice(0, 10)}.xlsx`
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
          // 处理中文文件名
          try {
            filename = decodeURIComponent(filename)
          } catch (e) {
            // 如果解码失败，使用原始文件名
          }
        }
      }
      
      // 创建 blob 并下载
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // 清理 URL 对象
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('导出成功')
    } else {
      // 处理错误
      try {
        const errorText = await response.text()
        console.error('导出失败:', response.status, errorText)
        
        // 尝试解析 JSON 错误信息
        try {
          const errorData = JSON.parse(errorText)
          if (errorData.msg) {
            ElMessage.error(`导出失败: ${errorData.msg}`)
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        } catch (parseError) {
          throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
        }
      } catch (textError) {
        throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
      }
    }
  } catch (error) {
    console.error('导出异常:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}
```

#### 使用示例

```html
<LbButton
  size="default"
  type="success"
  icon="Download"
  @click="handleExport"
  :loading="exportLoading"
>
  导出
</LbButton>
```

```javascript
// 导出按钮点击事件
const handleExport = () => {
  // 构建导出参数
  const exportParams = {
    title: searchForm.title || undefined,
    status: searchForm.status || undefined,
    category_id: searchForm.category_id || undefined,
    startTime: searchForm.startTime || undefined,
    endTime: searchForm.endTime || undefined
  }
  
  // 调用下载方法
    downloadFileByPost(exportParams)
  }
  ```

  ### 3. 图片上传功能

  图片上传功能用于上传服务图片、轮播图、分类图标等。

  #### 单图上传组件

  ```html
  <template>
    <div class="image-uploader">
      <el-upload
        class="avatar-uploader"
        :action="uploadUrl"
        :headers="headers"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="beforeUpload"
      >
        <img v-if="modelValue" :src="modelValue" class="avatar" />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
      </el-upload>
      <div class="upload-tip" v-if="showTip">
        请上传 JPG/PNG 格式图片，大小不超过 2MB
      </div>
    </div>
  </template>

  <script setup>
  import { ref, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'

  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    },
    showTip: {
      type: Boolean,
      default: true
    },
    maxSize: {
      type: Number,
      default: 2 // 默认 2MB
    }
  })

  const emit = defineEmits(['update:modelValue'])

  // 上传地址
  const uploadUrl = '/api/admin/upload/image'

  // 上传请求头
  const headers = computed(() => {
    const token = sessionStorage.getItem('minitk')
    return token ? { Authorization: `Bearer ${token}` } : {}
  })

  // 上传前验证
  const beforeUpload = (file) => {
    // 验证文件类型
    const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
    if (!isImage) {
      ElMessage.error('上传图片只能是 JPG/PNG 格式!')
      return false
    }
  
    // 验证文件大小
    const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
    if (!isLtMaxSize) {
      ElMessage.error(`上传图片大小不能超过 ${props.maxSize}MB!`)
      return false
    }
  
    return true
  }

  // 上传成功回调
  const handleSuccess = (res, file) => {
    if (res.code === 200) {
      emit('update:modelValue', res.data.url)
      ElMessage.success('上传成功')
    } else {
      ElMessage.error(res.msg || '上传失败')
    }
  }

  // 上传失败回调
  const handleError = (err) => {
    console.error('上传失败:', err)
    ElMessage.error('上传失败，请稍后重试')
  }
  </script>

  <style scoped>
  .image-uploader {
    text-align: center;
  }

  .avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 178px;
    height: 178px;
    display: inline-block;
  }

  .avatar-uploader:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
    object-fit: cover;
  }

  .upload-tip {
    font-size: 12px;
    color: #606266;
    margin-top: 5px;
  }
  </style>
  ```

  #### 使用示例

  ```html
  <el-form-item label="服务图片" prop="img">
    <ImageUploader v-model="form.img" />
  </el-form-item>
  ```

  ```javascript
  import ImageUploader from '@/components/common/ImageUploader.vue'

  // 在组件中注册
  const components = {
    ImageUploader
  }
  ```

  #### 多图上传组件

  ```html
  <template>
    <div class="multi-image-uploader">
      <el-upload
        class="image-list"
        :action="uploadUrl"
        :headers="headers"
        :file-list="fileList"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-remove="handleRemove"
        :before-upload="beforeUpload"
        list-type="picture-card"
        :limit="limit"
        :on-exceed="handleExceed"
      >
        <el-icon><Plus /></el-icon>
      </el-upload>
      <div class="upload-tip" v-if="showTip">
        请上传 JPG/PNG 格式图片，大小不超过 2MB，最多 {{ limit }} 张
      </div>
    </div>
  </template>

  <script setup>
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'

  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => []
    },
    showTip: {
      type: Boolean,
      default: true
    },
    maxSize: {
      type: Number,
      default: 2 // 默认 2MB
    },
    limit: {
      type: Number,
      default: 5 // 默认最多 5 张
    }
  })

  const emit = defineEmits(['update:modelValue'])

  // 上传地址
  const uploadUrl = '/api/admin/upload/image'

  // 上传请求头
  const headers = computed(() => {
    const token = sessionStorage.getItem('minitk')
    return token ? { Authorization: `Bearer ${token}` } : {}
  })

  // 文件列表
  const fileList = ref([])

  // 监听 modelValue 变化，更新文件列表
  watch(() => props.modelValue, (newVal) => {
    fileList.value = newVal.map((url, index) => ({
      name: `图片${index + 1}`,
      url
    }))
  }, { immediate: true })

  // 上传前验证
  const beforeUpload = (file) => {
    // 验证文件类型
    const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
    if (!isImage) {
      ElMessage.error('上传图片只能是 JPG/PNG 格式!')
      return false
    }
  
    // 验证文件大小
    const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
    if (!isLtMaxSize) {
      ElMessage.error(`上传图片大小不能超过 ${props.maxSize}MB!`)
      return false
    }
  
    return true
  }

  // 上传成功回调
  const handleSuccess = (res, file) => {
    if (res.code === 200) {
      const newUrls = [...props.modelValue, res.data.url]
      emit('update:modelValue', newUrls)
      ElMessage.success('上传成功')
    } else {
      ElMessage.error(res.msg || '上传失败')
    }
  }

  // 上传失败回调
  const handleError = (err) => {
    console.error('上传失败:', err)
    ElMessage.error('上传失败，请稍后重试')
  }

  // 移除图片回调
  const handleRemove = (file) => {
    const index = fileList.value.findIndex(item => item.url === file.url)
    if (index !== -1) {
      const newUrls = [...props.modelValue]
      newUrls.splice(index, 1)
      emit('update:modelValue', newUrls)
    }
  }

  // 超出限制回调
  const handleExceed = () => {
    ElMessage.warning(`最多只能上传 ${props.limit} 张图片!`)
  }
  </script>

  <style scoped>
  .multi-image-uploader {
    text-align: left;
  }

  .upload-tip {
    font-size: 12px;
    color: #606266;
    margin-top: 5px;
  }
  </style>
  ```

  #### 使用示例

  ```html
  <el-form-item label="服务图集" prop="images">
    <MultiImageUploader v-model="form.images" :limit="10" />
  </el-form-item>
  ```

  ```javascript
  import MultiImageUploader from '@/components/common/MultiImageUploader.vue'

  // 在组件中注册
  const components = {
    MultiImageUploader
  }
  ```

  ## 常见问题

### 1. 图片上传问题

**问题**: 上传图片时出现 "上传失败" 错误。

**解决方案**:
- 检查图片大小是否超过限制（通常为 2MB）
- 检查图片格式是否支持（通常支持 jpg, png, gif）
- 检查上传接口是否正确配置

**示例代码**:
```html
<el-upload
  class="avatar-uploader"
  :action="uploadUrl"
  :headers="headers"
  :show-file-list="false"
  :on-success="handleAvatarSuccess"
  :before-upload="beforeAvatarUpload"
>
  <img v-if="form.img" :src="form.img" class="avatar" />
  <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
</el-upload>
```

```javascript
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}
```

### 2. 表格数据不显示问题

**问题**: 表格数据获取成功但不显示。

**解决方案**:
- 检查表格的 `prop` 属性是否与数据字段名称一致
- 检查数据结构是否符合预期
- 使用 Vue DevTools 检查数据是否正确加载

**示例代码**:
```html
<!-- 确保prop与数据字段名称一致 -->
<el-table-column prop="title" label="标题" />
```

### 3. 表单验证问题

**问题**: 表单提交时验证不通过或验证规则不生效。

**解决方案**:
- 确保表单项的 `prop` 属性与表单数据的字段名一致
- 检查验证规则是否正确配置
- 确保调用了 `validate` 方法进行验证

**示例代码**:
```javascript
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}
```

```javascript
const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 提交表单
    } else {
      console.log('表单验证失败')
      return false
    }
  })
}
```

### 4. 状态切换问题

**问题**: 状态切换后页面没有更新或显示错误。

**解决方案**:
- 确保状态更新API调用成功
- 检查状态值是否正确（通常为 1/-1）
- 在API调用成功后更新本地数据

**示例代码**:
```javascript
const handleStatusChange = async (row) => {
  try {
    const res = await api.service.status({
      id: row.id,
      status: row.status === 1 ? -1 : 1
    })
    if (res.code === 200) {
      // 更新本地数据
      row.status = row.status === 1 ? -1 : 1
      ElMessage.success('状态更新成功')
    } else {
      ElMessage.error(res.msg || '状态更新失败')
    }
  } catch (error) {
    console.error('状态更新失败', error)
    ElMessage.error('状态更新失败')
  }
}
```

---

本文档由开发团队维护，最后更新日期：2023年12月15日