<!--
  LbImage 通用图片组件

  支持图片预览、懒加载、错误处理等功能
-->

<template>
  <div class="lb-image" :style="containerStyle">
    <el-image
      :src="imageSrc"
      :fit="fit"
      :lazy="lazy"
      :preview-src-list="previewList"
      :z-index="zIndex"
      :style="imageStyle"
      @load="handleLoad"
      @error="handleError"
    >
      <template #placeholder>
        <div class="image-placeholder">
          <el-icon><Picture /></el-icon>
          <span>加载中...</span>
        </div>
      </template>
      <template #error>
        <div class="image-error">
          <el-icon><PictureFilled /></el-icon>
          <span>加载失败</span>
        </div>
      </template>
    </el-image>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { Picture, PictureFilled } from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  width: {
    type: [String, Number],
    default: 'auto'
  },
  height: {
    type: [String, Number],
    default: 'auto'
  },
  fit: {
    type: String,
    default: 'cover',
    validator: (value) => ['fill', 'contain', 'cover', 'none', 'scale-down'].includes(value)
  },
  lazy: {
    type: Boolean,
    default: true
  },
  preview: {
    type: Boolean,
    default: true
  },
  zIndex: {
    type: Number,
    default: 2000
  },
  defaultSrc: {
    type: String,
    default: ''
  }
})

// Emits定义
const emit = defineEmits(['load', 'error'])

// 响应式数据
const loading = ref(false)
const error = ref(false)

// 计算属性
const imageSrc = computed(() => {
  if (props.src) {
    return props.src
  }
  if (props.defaultSrc) {
    return props.defaultSrc
  }
  return ''
})

const previewList = computed(() => {
  return props.preview && props.src ? [props.src] : []
})

const containerStyle = computed(() => {
  const style = {}
  
  if (props.width !== 'auto') {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }
  
  if (props.height !== 'auto') {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  }
  
  return style
})

const imageStyle = computed(() => {
  return {
    width: '100%',
    height: '100%'
  }
})

// 方法
const handleLoad = (event) => {
  loading.value = false
  error.value = false
  emit('load', event)
}

const handleError = (event) => {
  loading.value = false
  error.value = true
  emit('error', event)
}
</script>

<style scoped>
.lb-image {
  display: inline-block;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f5f7fa;
}

/* 默认尺寸 -   */
.lb-image {
  width: 60px;
  height: 60px;
}

/* 占位符样式 */
.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.image-placeholder .el-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

/* 错误状态样式 */
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #c0c4cc;
  font-size: 12px;
}

.image-error .el-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

/* 表格中的图片样式 */
.el-table .lb-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
}

/* 详情页面的图片样式 */
.detail-image .lb-image {
  width: 120px;
  height: 120px;
}

/* 缩略图样式 */
.thumbnail .lb-image {
  width: 40px;
  height: 40px;
}

/* 大图样式 */
.large-image .lb-image {
  width: 200px;
  height: 200px;
}
</style>
