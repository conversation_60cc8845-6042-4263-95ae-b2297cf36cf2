/**
 * 常量路由配置
 * 不需要权限验证的基础路由
 */

import LayoutContainer from '@/components/layout/LayoutContainer.vue'

export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginView.vue'),
    meta: {
      title: '登录',
      hidden: true,
      noAuth: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/RegisterView.vue'),
    meta: {
      title: '注册',
      hidden: true,
      noAuth: true
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/views/auth/ForgotPasswordView.vue'),
    meta: {
      title: '忘记密码',
      hidden: true,
      noAuth: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404View.vue'),
    meta: {
      title: '页面不存在',
      hidden: true,
      noAuth: true
    }
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403View.vue'),
    meta: {
      title: '访问被拒绝',
      hidden: true,
      noAuth: true
    }
  },
  {
    path: '/500',
    name: 'ServerError',
    component: () => import('@/views/error/500View.vue'),
    meta: {
      title: '服务器错误',
      hidden: true,
      noAuth: true
    }
  },
  {
    path: '/',
    component: LayoutContainer,
    redirect: '/service/list',
    meta: {
      title: '首页',
      icon: 'House'
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/DashboardView.vue'),
        meta: {
          title: '仪表盘',
          icon: 'Odometer',
          affix: true
        }
      }
    ]
  },

  // 服务项目模块路由
  {
    path: '/service',
    name: 'Service',
    component: LayoutContainer,
    redirect: '/service/list',
    meta: {
      title: '服务管理',
      icon: 'Service',
      menuName: 'Service'
    },
    children: [
      {
        path: 'list',
        name: 'ServiceList',
        component: () => import('@/views/service/ServiceList.vue'),
        meta: {
          title: '服务项目',
          keepAlive: true
        }
      },
          
      {
        path: 'edit',
        name: 'ServiceEdit',
        component: () => import('@/views/service/ServiceEdit.vue'),
        meta: {
          title: '编辑服务项目',
          hidden: true
        }
      },
      {
        path: 'banner',
        name: 'ServiceBanner',
        component: () => import('@/views/service/ServiceBanner.vue'),
        meta: {
          title: '轮播图设置'
        }
      },
      {
        path: 'jingang',
        name: 'ServiceJingang',
        component: () => import('@/views/service/ServiceJingang.vue'),
        meta: {
          title: '金刚区设置'
        }
      },
      {
        path: 'fenlei',
        name: 'ServiceFenlei',
        component: () => import('@/views/service/ServiceFenlei.vue'),
        meta: {
          title: '分类设置'
        }
      },
      {
        path: 'daili',
        name: 'ServiceDaili',
        component: () => import('@/views/service/ServiceDaili.vue'),
        meta: {
          title: '服务点设置'
        }
      },
      {
        path: 'peizhi',
        name: 'ServicePeizhi',
        component: () => import('@/views/service/ServicePeizhi.vue'),
        meta: {
          title: '项目配置'
        }
      },
    {
      path: 'text',
      name: 'ServiceText',
      component: () => import('@/views/service/ServiceText.vue'),
      meta: { title: '测试项目' }
    }
    ]
  },

  // 师傅管理模块路由
  {
    path: '/technician',
    name: 'Technician',
    component: LayoutContainer,
    redirect: '/technician/list',
    meta: {
      title: '师傅管理',
      icon: 'User',
      menuName: 'Technician'
    },
    children: [
      {
        path: 'list',
        name: 'TechnicianList',
        component: () => import('@/views/technician/TechnicianList.vue'),
        meta: {
          title: '师傅列表',
          keepAlive: true
        }
      },
      {
        path: 'edit',
        name: 'TechnicianEdit',
        component: () => import('@/views/technician/TechnicianEdit.vue'),
        meta: {
          title: '新增师傅',
          hidden: true
        }
      },
      {
        path: 'level',
        name: 'TechnicianLevel',
        component: () => import('@/views/technician/TechnicianLevel.vue'),
        meta: {
          title: '师傅等级'
        }
      },
      {
        path: 'deposit',
        name: 'TechnicianDeposit',
        component: () => import('@/views/technician/TechnicianDeposit.vue'),
        meta: {
          title: '师傅押金'
        }
      },
      {
        path: 'distance',
        name: 'TechnicianDistance',
        component: () => import('@/views/technician/TechnicianDistance.vue'),
        meta: {
          title: '接单范围'
        }
      },
      {
        path: 'log',
        name: 'TechnicianLog',
        component: () => import('@/views/technician/TechnicianLog.vue'),
        meta: {
          title: '日志管理'
        }
      },
      {
        path: 'city',
        name: 'TechnicianCity',
        component: () => import('@/views/technician/TechnicianCity.vue'),
        meta: {
          title: '城市管理'
        }
      },
      {
        path: 'blacklist',
        name: 'TechnicianBlacklist',
        component: () => import('@/views/technician/BlackList.vue'),
        meta: {
          title: '黑名单管理'
        }
      },
    ]
  },

  // 营销管理模块路由
  {
    path: '/market',
    name: 'Market',
    component: LayoutContainer,
    redirect: '/market/list',
    meta: {
      title: '营销管理',
      icon: 'Promotion',
      menuName: 'Market'
    },
    children: [
      {
        path: 'list',
        name: 'MarketList',
        component: () => import('@/views/market/MarketList.vue'),
        meta: {
          title: '卡券管理',
          keepAlive: true
        }
      },
      {
        path: 'edit',
        name: 'MarketEdit',
        component: () => import('@/views/market/MarketEdit.vue'),
        meta: {
          title: '编辑卡券',
          hidden: true
        }
      },
      {
        path: 'activity',
        name: 'MarketActivity',
        component: () => import('@/views/market/MarketActivity.vue'),
        meta: {
          title: '公告设置'
        }
      },
      {
        path: 'notice',
        name: 'MarketNotice',
        component: () => import('@/views/market/MarketNotice.vue'),
        meta: {
          title: '公告设置'
        }
      },
      {
        path: 'partner',
        name: 'MarketPartner',
        component: () => import('@/views/market/MarketPartner.vue'),
        meta: {
          title: '合伙人管理'
        }
      },
      {
        path: 'partner/invite',
        name: 'MarketPartnerInvite',
        component: () => import('@/views/market/MarketPartnerInvite.vue'),
        meta: {
          title: '合伙人邀请列表',
          hidden: true
        }
      },
      {
        path: 'partner/commission',
        name: 'MarketPartnerCommission',
        component: () => import('@/views/market/MarketPartnerCommission.vue'),
        meta: {
          title: '合伙人佣金统计',
          hidden: true
        }
      },
      {
        path: 'partner/orders',
        name: 'MarketPartnerOrders',
        component: () => import('@/views/market/MarketPartnerOrders.vue'),
        meta: {
          title: '合伙人推广订单',
          hidden: true
        }
      }
    ]
  },
// 订单管理模块路由
  {
    path: '/shop',
    name: 'Shop',
    component: LayoutContainer,
    redirect: '/shop/order',
    meta: {
      title: '订单管理',
      icon: 'Document',
      menuName: 'Shop',
      roles: ['admin', 'manager']
    },
    children: [
      {
        path: 'order',
        name: 'ShopOrder',
        component: () => import('@/views/shop/ShopOrder.vue'),
        meta: {
          title: '订单管理',
          keepAlive: true
        }
      },
      {
        path: 'order/detail',
        name: 'ShopOrderDetail',
        component: () => import('@/views/shop/ShopOrderDetail.vue'),
        meta: {
          title: '订单详情',
          hidden: true
        }
      },
      {
        path: 'refund',
        name: 'ShopRefund',
        component: () => import('@/views/shop/ShopRefund.vue'),
        meta: {
          title: '退款管理'
        }
      },
      {
        path: 'evaluate',
        name: 'ShopEvaluate',
        component: () => import('@/views/shop/ShopEvaluate.vue'),
        meta: {
          title: '评价管理'
        }
      },
      {
        path: 'commission',
        name: 'ShopCommission',
        component: () => import('@/views/shop/ShopCommission.vue'),
        meta: {
          title: '分销佣金'
        }
      },
      {
        path: 'aftersale',
        name: 'ShopAfterSale',
        component: () => import('@/views/shop/ShopAfterSale.vue'),
        meta: {
          title: '售后管理'
        }
      }
    ]
  },


  // 分销管理模块路由
  {
    path: '/distribution',
    name: 'Distribution',
    component: LayoutContainer,
    redirect: '/distribution/examine',
    meta: {
      title: '分销管理',
      icon: 'Share',
      menuName: 'Distribution'
    },
    children: [
      {
        path: 'examine',
        name: 'DistributionExamine',
        component: () => import('@/views/distribution/DistributionExamine.vue'),
        meta: {
          title: '分销审核',
          keepAlive: true
        }
      },
      {
        path: 'list',
        name: 'DistributionList',
        component: () => import('@/views/distribution/DistributionList.vue'),
        meta: {
          title: '分销商列表'
        }
      },
      {
        path: 'set',
        name: 'DistributionSet',
        component: () => import('@/views/distribution/DistributionSet.vue'),
        meta: {
          title: '分销设置'
        }
      }
    ]
  },

  // 财务管理模块路由
  {
    path: '/finance',
    name: 'Finance',
    component: LayoutContainer,
    redirect: '/finance/list',
    meta: {
      title: '财务管理',
      icon: 'Money',
      menuName: 'Finance'
    },
    children: [
      {
        path: 'list',
        name: 'FinanceList',
        component: () => import('@/views/finance/FinanceList.vue'),
        meta: {
          title: '财务列表',
          keepAlive: true
        }
      },
      {
        path: 'detail',
        name: 'FinanceDetail',
        component: () => import('@/views/finance/FinanceDetail.vue'),
        meta: {
          title: '财务详情',
          hidden: true
        }
      },
      {
        path: 'withdraw',
        name: 'FinanceWithdraw',
        component: () => import('@/views/finance/FinanceWithdraw.vue'),
        meta: {
          title: '提现管理'
        }
      },
      {
        path: 'stored',
        name: 'FinanceStored',
        component: () => import('@/views/finance/FinanceStored.vue'),
        meta: {
          title: '储值管理'
        }
      },
      {
        path: 'withdraw-history',
        name: 'FinanceWithdrawHistory',
        component: () => import('@/views/finance/FinanceWithdrawHistory.vue'),
        meta: {
          title: '历史提现'
        }
      }
    ]
  },

  // 用户管理模块路由
  {
    path: '/user',
    name: 'User',
    component: LayoutContainer,
    redirect: '/user/list',
    meta: {
      title: '用户管理',
      icon: 'UserFilled',
      menuName: 'User'
    },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/user/UserList.vue'),
        meta: {
          title: '用户列表',
          keepAlive: true
        }
      },
      {
        path: 'detail/:id',
        name: 'UserDetail',
        component: () => import('@/views/user/UserDetail.vue'),
        meta: {
          title: '用户详情',
          hidden: true
        }
      },
      {
        path: 'log',
        name: 'UserLog',
        component: () => import('@/views/user/UserLog.vue'),
        meta: {
          title: '操作日志'
        }
      }
    ]
  },

  // 账号设置模块路由
  {
    path: '/account',
    name: 'Account',
    component: LayoutContainer,
    redirect: '/account/admin',
    meta: {
      title: '账号设置',
      icon: 'Setting',
      menuName: 'Account'
    },
    children: [
      {
        path: 'admin',
        name: 'AccountAdmin',
        component: () => import('@/views/account/AccountAdmin.vue'),
        meta: {
          title: '管理员管理',
          keepAlive: true
        }
      },
      {
        path: 'role',
        name: 'AccountRole',
        component: () => import('@/views/account/AccountRole.vue'),
        meta: {
          title: '角色管理'
        }
      },
      {
        path: 'menu',
        name: 'AccountMenu',
        component: () => import('@/views/account/AccountMenu.vue'),
        meta: {
          title: '菜单管理'
        }
      },
      {
        path: 'franchisee',
        name: 'AccountFranchisee',
        component: () => import('@/views/account/AccountFranchisee.vue'),
        meta: {
          title: '代理商管理'
        }
      },
      {
        path: 'third',
        name: 'AccountThird',
        component: () => import('@/views/account/AccountThird.vue'),
        meta: {
          title: '第三方管理'
        }
      }
    ]
  },

  // 系统设置模块路由
  {
    path: '/sys',
    name: 'System',
    component: LayoutContainer,
    redirect: '/sys/wechat',
    meta: {
      title: '系统设置',
      icon: 'Tools',
      menuName: 'System'
    },
    children: [
      // 版本管理
      {
        path: 'upgrade',
        name: 'SystemUpgrade',
        component: () => import('@/views/system/SystemUpgrade.vue'),
        meta: {
          title: '系统升级'
        }
      },
      {
        path: 'examine',
        name: 'SystemExamineOld',
        component: () => import('@/views/system/SystemExamine.vue'),
        meta: {
          title: '上传微信审核'
        }
      },
      // 系统设置
      {
        path: 'wechat',
        name: 'SystemWechat',
        component: () => import('@/views/system/SystemWechat.vue'),
        meta: {
          title: '小程序设置'
        }
      },
      {
        path: 'web',
        name: 'SystemWeb',
        component: () => import('@/views/system/SystemWeb.vue'),
        meta: {
          title: '公众号设置'
        }
      },
      {
        path: 'app',
        name: 'SystemApp',
        component: () => import('@/views/system/SystemApp.vue'),
        meta: {
          title: 'APP设置'
        }
      },
      {
        path: 'info',
        name: 'SystemInfo',
        component: () => import('@/views/system/SystemInfo.vue'),
        meta: {
          title: '隐私协议'
        }
      },
      {
        path: 'payment',
        name: 'SystemPayment',
        component: () => import('@/views/system/SystemPayment.vue'),
        meta: {
          title: '支付配置'
        }
      },
      {
        path: 'upload',
        name: 'SystemUpload',
        component: () => import('@/views/system/SystemUpload.vue'),
        meta: {
          title: '上传配置'
        }
      },
      {
        path: 'transaction',
        name: 'SystemTransaction',
        component: () => import('@/views/system/SystemTransaction.vue'),
        meta: {
          title: '交易设置'
        }
      },
      {
        path: 'notice',
        name: 'SystemNotice',
        component: () => import('@/views/system/SystemNotice.vue'),
        meta: {
          title: '万能通知'
        }
      },
      {
        path: 'message',
        name: 'SystemMessage',
        component: () => import('@/views/system/SystemMessage.vue'),
        meta: {
          title: '短信通知'
        }
      },
      {
        path: 'information',
        name: 'SystemInformation',
        component: () => import('@/views/system/SystemInformation.vue'),
        meta: {
          title: '备案信息'
        }
      },
      // 其他设置
      {
        path: 'print',
        name: 'SystemPrint',
        component: () => import('@/views/system/SystemPrint.vue'),
        meta: {
          title: '打印机设置'
        }
      },
      {
        path: 'car_fee',
        name: 'SystemCarFee',
        component: () => import('@/views/system/SystemCarFee.vue'),
        meta: {
          title: '车费设置'
        }
      },
      {
        path: 'city',
        name: 'SystemCity',
        component: () => import('@/views/system/SystemCity.vue'),
        meta: {
          title: '城市设置'
        }
      },
      {
        path: 'travel',
        name: 'SystemTravel',
        component: () => import('@/views/system/SystemTravel.vue'),
        meta: {
          title: '出行设置'
        }
      },
      {
        path: 'other',
        name: 'SystemOther',
        component: () => import('@/views/system/SystemOther.vue'),
        meta: {
          title: '其他设置'
        }
      },
      {
        path: 'version',
        name: 'SystemVersion',
        component: () => import('@/views/system/SystemVersion.vue'),
        meta: {
          title: '版本管理'
        }
      }
    ]
  },

  // 日志管理模块路由
  {
    path: '/log',
    name: 'Log',
    component: LayoutContainer,
    redirect: '/log/operation',
    meta: {
      title: '日志管理',
      icon: 'Document',
      menuName: 'Log',
      roles: ['admin', 'manager']
    },
    children: [
      {
        path: 'operation',
        name: 'LogOperation',
        component: () => import('@/views/log/OperationLog.vue'),
        meta: {
          title: '操作日志',
          keepAlive: true
        }
      }
    ]
  },
  // 重定向到404页面
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
    meta: {
      hidden: true
    }
  }
]
